use crate::soa;

// Example usage of the Structure of Arrays macro

// Define a simple 3D vector type for demonstration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct Vec3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vec3 {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }
}

// Generate a Structure of Arrays for particle data
soa! {
    /// A Structure of Arrays for particle simulation
    pub struct ParticleData {
        /// Position of each particle
        pub position: Vec3,
        /// Velocity of each particle  
        pub velocity: Vec3,
        /// Mass of each particle
        pub mass: f32,
        /// Unique identifier for each particle
        pub id: u32,
    }
}

// Generate another SoA for transform data
soa! {
    /// Transform data for entities
    pub struct TransformData {
        /// Translation
        pub translation: Vec3,
        /// Rotation (as quaternion components)
        pub rotation: [f32; 4],
        /// Scale
        pub scale: Vec3,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_particle_soa_basic_operations() {
        let mut particles = ParticleData::new();
        
        // Test empty state
        assert_eq!(particles.len(), 0);
        assert!(particles.is_empty());
        
        // Add some particles
        particles.push(ParticleDataValue {
            position: Vec3::new(1.0, 2.0, 3.0),
            velocity: Vec3::new(0.1, 0.2, 0.3),
            mass: 1.5,
            id: 100,
        });
        
        particles.push_fields(
            Vec3::new(4.0, 5.0, 6.0),  // position
            Vec3::new(0.4, 0.5, 0.6),  // velocity
            2.0,                        // mass
            101,                        // id
        );
        
        // Test size
        assert_eq!(particles.len(), 2);
        assert!(!particles.is_empty());
        
        // Test field access
        assert_eq!(particles.position_at(0), Some(&Vec3::new(1.0, 2.0, 3.0)));
        assert_eq!(particles.mass_at(1), Some(&2.0));
        assert_eq!(particles.id_at(0), Some(&100));
        
        // Test slices
        let positions = particles.position_slice();
        assert_eq!(positions.len(), 2);
        assert_eq!(positions[0], Vec3::new(1.0, 2.0, 3.0));
        assert_eq!(positions[1], Vec3::new(4.0, 5.0, 6.0));
        
        // Test get method
        if let Some(particle_ref) = particles.get(0) {
            assert_eq!(*particle_ref.position, Vec3::new(1.0, 2.0, 3.0));
            assert_eq!(*particle_ref.mass, 1.5);
        }
        
        // Test mutable access
        if let Some(mut particle_ref) = particles.get_mut(1) {
            *particle_ref.mass = 3.0;
        }
        assert_eq!(particles.mass_at(1), Some(&3.0));
    }
    
    #[test]
    fn test_particle_soa_iteration() {
        let mut particles = ParticleData::new();
        
        // Add test data
        for i in 0..5 {
            particles.push_fields(
                Vec3::new(i as f32, i as f32 * 2.0, i as f32 * 3.0),
                Vec3::zero(),
                1.0 + i as f32,
                i as u32,
            );
        }
        
        // Test const iteration
        let mut count = 0;
        for particle in particles.iter() {
            assert_eq!(particle.id, &(count as u32));
            assert_eq!(particle.mass, &(1.0 + count as f32));
            count += 1;
        }
        assert_eq!(count, 5);
        
        // Test mutable iteration
        for particle in particles.iter_mut() {
            *particle.mass *= 2.0;
        }
        
        // Verify changes
        for i in 0..5 {
            assert_eq!(particles.mass_at(i), Some(&(2.0 * (1.0 + i as f32))));
        }
    }
    
    #[test]
    fn test_particle_soa_collection_operations() {
        let mut particles = ParticleData::new();
        
        // Test with_capacity
        let mut particles_with_cap = ParticleData::with_capacity(10);
        assert_eq!(particles_with_cap.capacity(), 10);
        
        // Test from_iter
        let values: Vec<ParticleDataValue> = (0..3).map(|i| ParticleDataValue {
            position: Vec3::new(i as f32, 0.0, 0.0),
            velocity: Vec3::zero(),
            mass: 1.0,
            id: i as u32,
        }).collect();
        
        let particles_from_iter: ParticleData = values.into_iter().collect();
        assert_eq!(particles_from_iter.len(), 3);
        
        // Test extend
        let more_values: Vec<ParticleDataValue> = (3..5).map(|i| ParticleDataValue {
            position: Vec3::new(i as f32, 0.0, 0.0),
            velocity: Vec3::zero(),
            mass: 1.0,
            id: i as u32,
        }).collect();
        
        particles.extend(more_values);
        assert_eq!(particles.len(), 2);
    }
    
    #[test]
    fn test_transform_soa() {
        let mut transforms = TransformData::new();
        
        transforms.push_fields(
            Vec3::new(1.0, 2.0, 3.0),     // translation
            [0.0, 0.0, 0.0, 1.0],         // rotation (identity quaternion)
            Vec3::new(1.0, 1.0, 1.0),     // scale
        );
        
        assert_eq!(transforms.len(), 1);
        assert_eq!(transforms.translation_at(0), Some(&Vec3::new(1.0, 2.0, 3.0)));
        assert_eq!(transforms.rotation_at(0), Some(&[0.0, 0.0, 0.0, 1.0]));
    }
}
